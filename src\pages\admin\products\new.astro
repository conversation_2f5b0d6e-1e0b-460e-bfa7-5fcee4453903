---
import AdminLayout from '../../../layouts/AdminLayout.astro';
import { isAuthenticated } from '../../../utils/auth';

// Защита страницы
if (!isAuthenticated(Astro.request)) {
  return Astro.redirect('/admin/login');
}

// Загрузка данных о категориях для выпадающего списка
import categoriesFile from '../../../../data/product/categories.json';
// Фильтруем только активные категории (activeForProducts = true)
const categoriesData = categoriesFile.categories.filter(cat => cat.activeForProducts !== false);
---

<AdminLayout title="Добавить товар | LuxBeton">
  <div class="container mx-auto py-8 px-4">
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-3xl font-bold">Добавить новый товар</h1>
      <a href="/admin/products" class="bg-gray-300 text-gray-800 px-4 py-2 rounded hover:bg-gray-400">
        Назад к списку
      </a>
    </div>

    <form id="product-form" class="bg-white rounded-lg shadow-md p-6">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Основная информация -->
        <div>
          <h2 class="text-xl font-semibold mb-4">Основная информация</h2>

          <div class="mb-4">
            <label for="id" class="block text-sm font-medium text-gray-700 mb-1">ID товара</label>
            <div class="flex items-center space-x-2">
              <input
                type="text"
                id="id"
                placeholder="Будет сгенерирован автоматически"
                class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 bg-gray-50"
                readonly
                required
              />
              <button
                type="button"
                id="generate-id-btn"
                class="generate-id-btn px-3 py-2 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                style="background-color: #3b82f6;"
                disabled
              >
                Генерировать
              </button>
            </div>
            <p class="text-xs text-gray-500 mt-1">ID будет автоматически сгенерирован при выборе категории</p>
          </div>

          <div class="mb-4">
            <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Название товара</label>
            <input
              type="text"
              id="name"
              placeholder="Введите название товара"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              required
            />
          </div>

          <div class="mb-4">
            <label for="category" class="block text-sm font-medium text-gray-700 mb-1">Категория</label>
            <select
              id="category"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              required
            >
              <option value="">Выберите категорию</option>
              {categoriesData.map(cat => (
                <option value={cat.name} data-category-id={cat.id}>{cat.name}</option>
              ))}
            </select>
            <div id="category-info" class="text-xs text-gray-500 mt-1 hidden">
              <span id="category-id-prefix" class="font-medium"></span>
              <span> - префикс для ID товаров этой категории</span>
            </div>
          </div>

          <div class="mb-4">
            <label for="subcategory" class="block text-sm font-medium text-gray-700 mb-1">Подкатегория</label>
            <input
              type="text"
              id="subcategory"
              placeholder="Введите подкатегорию"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <div class="mb-4">
            <label for="product-slug" class="block text-sm font-medium text-gray-700 mb-1">SLUG (URL товара)</label>
            <div class="flex items-center space-x-2">
              <input
                type="text"
                id="product-slug"
                placeholder="url-slug-tovara"
                class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 font-mono text-sm"
              />
              <button
                type="button"
                id="generate-product-slug-btn"
                class="generate-product-slug-btn px-3 py-2 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm whitespace-nowrap"
                style="background-color: #3b82f6;"
                title="Генерировать SLUG из названия товара с транслитерацией"
              >
                Генерировать
              </button>
            </div>
            <p class="mt-1 text-xs text-gray-500">Автоматически генерируется из названия или нажмите кнопку для ручной генерации</p>
          </div>

          <div class="mb-4">
            <label for="shortDescription" class="block text-sm font-medium text-gray-700 mb-1">Краткое описание</label>
            <textarea
              id="shortDescription"
              placeholder="Краткое описание товара"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              rows="2"
              required
            ></textarea>
          </div>

          <div class="mb-4">
            <label for="fullDescription" class="block text-sm font-medium text-gray-700 mb-1">Полное описание</label>
            <textarea
              id="fullDescription"
              placeholder="Подробное описание товара"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              rows="5"
            ></textarea>
          </div>
        </div>

        <!-- Цена и атрибуты -->
        <div>
          <h2 class="text-xl font-semibold mb-4">Цена и атрибуты</h2>

          <div class="grid grid-cols-2 gap-4 mb-4">
            <div>
              <label for="price" class="block text-sm font-medium text-gray-700 mb-1">Цена</label>
              <input
                type="number"
                id="price"
                placeholder="0.00"
                step="0.01"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                required
              />
            </div>
            <div>
              <label for="unit" class="block text-sm font-medium text-gray-700 mb-1">Единица измерения</label>
              <input
                type="text"
                id="unit"
                placeholder="шт"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                required
              />
            </div>
          </div>

          <div class="mb-4">
            <label for="colors" class="block text-sm font-medium text-gray-700 mb-1">Доступные цвета (через запятую)</label>
            <input
              type="text"
              id="colors"
              placeholder="серый, красный, коричневый"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <div class="mb-4">
            <label for="texture" class="block text-sm font-medium text-gray-700 mb-1">Текстура</label>
            <input
              type="text"
              id="texture"
              placeholder="гладкая"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 mb-1">Размеры (мм)</label>
            <div class="grid grid-cols-3 gap-2">
              <div>
                <label for="length" class="block text-xs text-gray-500">Длина</label>
                <input
                  type="number"
                  id="length"
                  placeholder="200"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label for="width" class="block text-xs text-gray-500">Ширина</label>
                <input
                  type="number"
                  id="width"
                  placeholder="100"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label for="height" class="block text-xs text-gray-500">Высота</label>
                <input
                  type="number"
                  id="height"
                  placeholder="40"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
          </div>

          <div class="mb-4">
            <label for="weight" class="block text-sm font-medium text-gray-700 mb-1">Вес (кг)</label>
            <input
              type="number"
              id="weight"
              placeholder="8.5"
              step="0.1"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <div class="mb-4">
            <label for="strength" class="block text-sm font-medium text-gray-700 mb-1">Прочность</label>
            <input
              type="text"
              id="strength"
              placeholder="B22.5"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <div class="mb-4">
            <label for="inStock" class="flex items-center">
              <input
                type="checkbox"
                id="inStock"
                class="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                checked
              />
              <span class="ml-2 text-sm text-gray-700">Опубликован (доступен для просмотра на сайте)</span>
            </label>
          </div>
        </div>
      </div>

      <!-- Изображения -->
      <div class="mt-6">
        <h2 class="text-xl font-semibold mb-4">Изображения</h2>

        <div class="mb-4 p-4 bg-blue-50 border border-blue-200 rounded">
          <h3 class="text-sm font-medium text-blue-800 mb-2">Правила именования изображений:</h3>
          <ul class="text-xs text-blue-700 space-y-1">
            <li>• Главное изображение: будет названо как <code>название-товара_main.jpg</code></li>
            <li>• Дополнительные: будут названы как <code>название-товара_1.jpg</code>, <code>название-товара_2.jpg</code> и т.д.</li>
            <li>• Поддерживаются форматы: JPG, PNG</li>
          </ul>
        </div>

        <div class="mb-4">
          <label for="main-image" class="block text-sm font-medium text-gray-700 mb-1">Главное изображение</label>
          <input
            type="file"
            id="main-image"
            accept="image/*"
            class="w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
          />
          <div id="main-image-preview" class="mt-2 hidden">
            <img id="main-image-preview-img" src="" alt="Предпросмотр главного изображения" class="h-20 w-20 object-cover rounded border">
          </div>
        </div>

        <div class="mb-4">
          <label for="additional-images" class="block text-sm font-medium text-gray-700 mb-1">Дополнительные изображения (до 6 штук)</label>
          <input
            type="file"
            id="additional-images"
            accept="image/*"
            multiple
            class="w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
          />
          <div id="additional-images-preview" class="mt-2 grid grid-cols-6 gap-2 hidden">
            <!-- Предпросмотр дополнительных изображений будет добавлен динамически -->
          </div>
        </div>
      </div>

      <div class="mt-8 flex justify-end">
        <a href="/admin/products" class="bg-gray-300 text-gray-800 px-4 py-2 rounded mr-2 hover:bg-gray-400">
          Отмена
        </a>
        <button type="submit" class="create-product-btn text-white px-4 py-2 rounded" style="background-color: #3b82f6;">
          Создать товар
        </button>
      </div>
    </form>
  </div>
</AdminLayout>

<style>
  /* Стили для кнопки создания продукта */
  .create-product-btn:hover {
    background-color: #2563eb !important;
  }

  /* Стили для кнопки генерации ID */
  .generate-id-btn:hover {
    background-color: #2563eb !important;
  }

  /* Стили для кнопки генерации SLUG */
  .generate-product-slug-btn:hover {
    background-color: #2563eb !important;
  }
</style>

<script>
  // Обработчик отправки формы
  document.getElementById('product-form').addEventListener('submit', async (e) => {
    e.preventDefault();

    // Сбор данных формы
    const id = document.getElementById('id').value;
    const name = document.getElementById('name').value;
    const slug = document.getElementById('product-slug').value;
    const category = document.getElementById('category').value;
    const subcategory = document.getElementById('subcategory').value;
    const shortDescription = document.getElementById('shortDescription').value;
    const fullDescription = document.getElementById('fullDescription').value;
    const price = parseFloat(document.getElementById('price').value);
    const unit = document.getElementById('unit').value;
    const colors = document.getElementById('colors').value.split(',').map(c => c.trim()).filter(c => c);
    const texture = document.getElementById('texture').value;
    const length = parseInt(document.getElementById('length').value) || 0;
    const width = parseInt(document.getElementById('width').value) || 0;
    const height = parseInt(document.getElementById('height').value) || 0;
    const weight = parseFloat(document.getElementById('weight').value) || 0;
    const strength = document.getElementById('strength').value;
    const inStock = document.getElementById('inStock').checked;

    // Получение правильного categorySlug из данных категорий
    // Загружаем данные категорий для получения правильного slug
    let categorySlug = category.toLowerCase().replace(/\s+/g, '-');
    try {
      const categoriesResponse = await fetch('/data/product/categories.json');
      const categoriesData = await categoriesResponse.json();
      const foundCategory = categoriesData.categories.find(cat => cat.name === category);
      if (foundCategory) {
        categorySlug = foundCategory.slug;
      }
    } catch (error) {
      console.warn('Не удалось загрузить данные категорий, используется fallback slug:', error);
    }

    // Формирование объекта товара
    const productData = {
      id,
      name,
      slug,
      category,
      categorySlug,
      subcategory,
      shortDescription,
      fullDescription,
      price: {
        value: price,
        unit
      },
      attributes: {
        colors,
        color_pigments: {
          id: "no_pigment",
          name: "без красителя",
          description: "Изделие без добавления цветных пигментов, стандартный серый цвет."
        },
        texture,
        size: {
          length,
          width,
          height
        },
        weight,
        strength,
        surface: "одноуровневая",
        pattern: "нет рисунка"
      },
      images: {
        main: `${id}/${name.toLowerCase().replace(/[^а-яё\w\s]/gi, '').replace(/\s+/g, '-').substring(0, 30)}_main.jpg`,
        additional: []
      },
      inStock,
      popularity: 4.0
    };

    try {
      // Сначала создаем товар
      const response = await fetch('/api/admin/products', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(productData)
      });

      if (response.ok) {
        // Загружаем изображения, если они выбраны
        await uploadProductImages(id, name);

        await window.adminModal?.showSuccess('Товар успешно создан!');
        window.location.href = '/admin/products';
      } else {
        const error = await response.json();
        await window.adminModal?.showError('Ошибка при создании товара: ' + (error.error || 'Неизвестная ошибка'));
      }
    } catch (error) {
      console.error('Ошибка:', error);
      await window.adminModal?.showError('Произошла ошибка при создании товара. Проверьте подключение к интернету и попробуйте еще раз.');
    }
  });

  // Функция для загрузки изображений товара
  async function uploadProductImages(productId, productName) {
    const mainImageInput = document.getElementById('main-image');
    const additionalImagesInput = document.getElementById('additional-images');

    // Загружаем главное изображение
    if (mainImageInput.files && mainImageInput.files[0]) {
      await uploadSingleImage(mainImageInput.files[0], productId, productName, true);
    }

    // Загружаем дополнительные изображения
    if (additionalImagesInput.files) {
      for (let i = 0; i < Math.min(additionalImagesInput.files.length, 6); i++) {
        await uploadSingleImage(additionalImagesInput.files[i], productId, productName, false);
      }
    }
  }

  // Функция для загрузки одного изображения
  async function uploadSingleImage(file, productId, productName, isMain) {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('productId', productId);
    formData.append('imageType', isMain ? 'main' : 'additional');

    try {
      const response = await fetch('/api/admin/upload-image', {
        method: 'POST',
        body: formData
      });

      const result = await response.json();

      if (!result.success) {
        console.error('Ошибка при загрузке изображения:', result.error);
      } else {
        console.log('Изображение успешно загружено:', result.imagePath);
      }
    } catch (error) {
      console.error('Ошибка при загрузке изображения:', error);
    }
  }

  // Предпросмотр главного изображения
  document.getElementById('main-image').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = function(e) {
        const preview = document.getElementById('main-image-preview');
        const img = document.getElementById('main-image-preview-img');
        img.src = e.target.result;
        preview.classList.remove('hidden');
      };
      reader.readAsDataURL(file);
    }
  });

  // Предпросмотр дополнительных изображений
  document.getElementById('additional-images').addEventListener('change', function(e) {
    const files = Array.from(e.target.files).slice(0, 6); // Максимум 6 изображений
    const preview = document.getElementById('additional-images-preview');

    // Очищаем предыдущий предпросмотр
    preview.innerHTML = '';

    if (files.length > 0) {
      preview.classList.remove('hidden');

      files.forEach((file, index) => {
        const reader = new FileReader();
        reader.onload = function(e) {
          const imgContainer = document.createElement('div');
          imgContainer.className = 'relative';

          const img = document.createElement('img');
          img.src = e.target.result;
          img.alt = `Дополнительное изображение ${index + 1}`;
          img.className = 'h-16 w-16 object-cover rounded border';

          imgContainer.appendChild(img);
          preview.appendChild(imgContainer);
        };
        reader.readAsDataURL(file);
      });
    } else {
      preview.classList.add('hidden');
    }
  });

  // Автоматическая генерация ID товара при выборе категории
  document.getElementById('category').addEventListener('change', async function(e) {
    const selectedCategory = e.target.value;
    const selectedOption = e.target.selectedOptions[0];
    const categoryId = selectedOption ? selectedOption.getAttribute('data-category-id') : null;

    const idInput = document.getElementById('id');
    const generateBtn = document.getElementById('generate-id-btn');
    const categoryInfo = document.getElementById('category-info');
    const categoryIdPrefix = document.getElementById('category-id-prefix');

    if (selectedCategory && categoryId) {
      // Показываем информацию о категории
      categoryIdPrefix.textContent = categoryId;
      categoryInfo.classList.remove('hidden');

      // Включаем кнопку генерации
      generateBtn.disabled = false;

      // Автоматически генерируем ID
      await generateProductId(selectedCategory);
    } else {
      // Скрываем информацию и очищаем поля
      categoryInfo.classList.add('hidden');
      generateBtn.disabled = true;
      idInput.value = '';
    }
  });

  // Обработчик кнопки генерации ID
  document.getElementById('generate-id-btn').addEventListener('click', async function() {
    const category = document.getElementById('category').value;
    if (category) {
      await generateProductId(category);
    }
  });

  // Функция генерации ID товара
  async function generateProductId(categoryName) {
    const idInput = document.getElementById('id');
    const generateBtn = document.getElementById('generate-id-btn');

    try {
      // Показываем состояние загрузки
      generateBtn.textContent = 'Генерация...';
      generateBtn.disabled = true;
      idInput.value = 'Генерируется...';

      const response = await fetch(`/api/admin/generate-product-id?category=${encodeURIComponent(categoryName)}`);
      const result = await response.json();

      if (result.success) {
        idInput.value = result.productId;
        console.log('Сгенерирован ID товара:', result.productId);
      } else {
        alert('Ошибка при генерации ID: ' + (result.error || 'Неизвестная ошибка'));
        idInput.value = '';
      }
    } catch (error) {
      console.error('Ошибка при генерации ID товара:', error);
      alert('Ошибка при генерации ID товара');
      idInput.value = '';
    } finally {
      // Восстанавливаем состояние кнопки
      generateBtn.textContent = 'Генерировать';
      generateBtn.disabled = false;
    }
  }

  // Функция генерации SLUG с транслитерацией
  function generateSlugFromName(name) {
    if (!name) return '';

    // Маппинг кириллических символов в латинские для SLUG
    const cyrillicToLatin = {
      'А': 'a', 'Б': 'b', 'В': 'v', 'Г': 'g', 'Д': 'd', 'Е': 'e', 'Ё': 'yo', 'Ж': 'zh',
      'З': 'z', 'И': 'i', 'Й': 'y', 'К': 'k', 'Л': 'l', 'М': 'm', 'Н': 'n', 'О': 'o',
      'П': 'p', 'Р': 'r', 'С': 's', 'Т': 't', 'У': 'u', 'Ф': 'f', 'Х': 'h', 'Ц': 'ts',
      'Ч': 'ch', 'Ш': 'sh', 'Щ': 'sch', 'Ъ': '', 'Ы': 'y', 'Ь': '', 'Э': 'e', 'Ю': 'yu', 'Я': 'ya',
      'а': 'a', 'б': 'b', 'в': 'v', 'г': 'g', 'д': 'd', 'е': 'e', 'ё': 'yo', 'ж': 'zh',
      'з': 'z', 'и': 'i', 'й': 'y', 'к': 'k', 'л': 'l', 'м': 'm', 'н': 'n', 'о': 'o',
      'п': 'p', 'р': 'r', 'с': 's', 'т': 't', 'у': 'u', 'ф': 'f', 'х': 'h', 'ц': 'ts',
      'ч': 'ch', 'ш': 'sh', 'щ': 'sch', 'ъ': '', 'ы': 'y', 'ь': '', 'э': 'e', 'ю': 'yu', 'я': 'ya'
    };

    // Транслитерация кириллицы в латиницу
    let transliterated = '';
    for (let i = 0; i < name.length; i++) {
      const char = name[i];
      transliterated += cyrillicToLatin[char] || char;
    }

    // Создание SLUG: приведение к нижнему регистру, замена пробелов на дефисы, удаление недопустимых символов
    return transliterated
      .toLowerCase()
      .replace(/\s+/g, '-')           // Заменяем пробелы на дефисы
      .replace(/[^a-z0-9-]/g, '')     // Удаляем все символы кроме букв, цифр и дефисов
      .replace(/--+/g, '-')           // Заменяем множественные дефисы на одинарные
      .replace(/^-+/g, '')            // Удаляем дефисы в начале
      .replace(/-+$/g, '');           // Удаляем дефисы в конце
  }

  // Автоматическая генерация SLUG при вводе названия товара
  document.getElementById('name').addEventListener('input', function() {
    const productSlugInput = document.getElementById('product-slug');
    if (!productSlugInput.value) { // Генерируем только если поле пустое
      const slug = generateSlugFromName(this.value);
      productSlugInput.value = slug;
    }
  });

  // Обработчик кнопки генерации SLUG
  document.getElementById('generate-product-slug-btn').addEventListener('click', function() {
    const nameInput = document.getElementById('name');
    const productSlugInput = document.getElementById('product-slug');

    if (nameInput.value) {
      const slug = generateSlugFromName(nameInput.value);
      productSlugInput.value = slug;
    }
  });
</script>
